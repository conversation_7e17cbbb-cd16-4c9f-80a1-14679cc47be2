"""Models package for CANFAR API.

This package contains all the Pydantic models used throughout the Canfar API client.
The models are organized into separate modules for better maintainability:

- types: Common type definitions and constants (Kind, Status, View)
- session: Session-related models (CreateSpec, FetchSpec, FetchResponse)
- registry: Registry and discovery-related models (IVOASearchConfig, Servers, etc.)
- container: Container registry models (ContainerRegistry)
"""
