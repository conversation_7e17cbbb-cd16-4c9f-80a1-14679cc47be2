name: Edge Container Build

on:
  repository_dispatch:
    types: [edge-build]

env:
  GHCR_REGISTRY: ghcr.io
  IMAGE_NAME: opencadc/canfar
  IMAGE_TAG: edge

permissions:
  contents: read

jobs:
  edge-build:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      attestations: write
      id-token: write
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0080882f6c36860b6ba35c610c98ce87d4e2f26f # v2.10.2
        with:
          egress-policy: audit

      -
        name: Client Payload
        run: |
          echo "Client Payload: ${{ toJson(github.event.client_payload) }}"
      -
        name: Checkout Code
        uses: actions/checkout@eef61447b9ff4aafe5dcd4e0bbf5d482be7e7871 # v4.2.1
      -
        name: Setup Docker Buildx
        uses: docker/setup-buildx-action@c47758b77c9736f4b2ef4073d4d51994fabfe349 # v3.7.1
        with:
          install: true
      -
        name: Perform GHCR Login
        uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      -
        name: Build & Push Docker Image
        id: build
        uses: docker/build-push-action@48aba3b46d1b1fec4febb7c5d0c644b249a11355 # v6.10.0
        with:
          context: .
          target: production
          file: Dockerfile
          platforms: linux/amd64,linux/arm64
          cache-from: type=gha
          cache-to: type=gha,mode=max
          provenance: mode=max
          sbom: true
          push: true
          labels: |
            org.opencontainers.image.title=canfar
            org.opencontainers.image.version=edge
            org.opencontainers.image.description='Python Client for CANFAR Science Platform'
            org.opencontainers.image.licenses=AGPL-3.0
            org.opencontainers.image.source=https://github.com/opencadc/canfar
          tags: |
            ${{ env.GHCR_REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
      -
        name: Attest GHCR Container Image
        uses: actions/attest-build-provenance@ef244123eb79f2f7a7e75d99086184180e6d0018 # v1.4.4
        with:
          subject-name: ${{ env.GHCR_REGISTRY }}/${{ env.IMAGE_NAME }}
          subject-digest: ${{ steps.build.outputs.digest }}
          push-to-registry: true
