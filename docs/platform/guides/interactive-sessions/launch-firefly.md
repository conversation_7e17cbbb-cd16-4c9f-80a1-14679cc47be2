# Launching a Firefly Session

**The LSST table and image visualiser for astronomical data exploration**

!!! abstract "🎯 What You'll Learn"
    - How to launch a Firefly session and choose the right version
    - How to load images, tables, and access CANFAR storage
    - How to perform catalog overlays, plotting, and cutouts
    - Performance tips for large surveys and troubleshooting guidance

Firefly is a powerful web-based visualisation tool originally developed for the Rubin Observatory LSST. It provides advanced capabilities for viewing images, overlaying catalogues, and analysing tabular data - making it perfect for survey data analysis and multi-wavelength astronomy.

## 🎯 What is Firefly?

Firefly offers specialised tools for:

- **Image visualisation** with advanced stretch and colour controls
- **Catalogue overlay** and source analysis tools  
- **Table viewer** with filtering, plotting, and statistical tools
- **Multi-wavelength data** comparison and analysis
- **Large survey datasets** like LSST, HSC, and WISE

### Key Features

| Feature | Capability |
|---------|------------|
| **Image Display** | FITS images with WCS support, multiple panels |
| **Catalogue Overlay** | Plot sources on images, interactive selection |
| **Table Analysis** | Sort, filter, plot columns, statistical analysis |
| **Multi-band** | RGB colour composites, band switching |
| **Cutout Services** | Extract subimages from large surveys |
| **Coordinate Systems** | Support for all standard astronomical coordinates |

## 🚀 Launching Firefly

### Step 1: Create New Session

1. **Login** to the [CANFAR Science Portal](https://www.canfar.net/science-portal)
2. **Click** the plus sign (**+**) to create a new session
3. **Select** `firefly` as your session type

![Select Firefly Session](images/firefly/1_select_firefly_session.png)

### Step 2: Choose Container

The container selection updates automatically after choosing the session type. Select the Firefly container version you need:

- **firefly:latest** - Most recent stable version (recommended)
- **firefly:X.X** - Specific version for reproducible analysis

![Choose Firefly Container](images/firefly/2_select_firefly_container.png)

### Step 3: Configure Session

#### Session Name
Choose a descriptive name that helps identify your work:
- `lsst-photometry`
- `hsc-catalog-analysis` 
- `multiband-survey`

![Name Firefly Session](images/firefly/3_choose_firefly_name.png)

#### Memory Requirements

If using a fixed resource session, select RAM based on your data size:

- **8GB**: Small catalogues, single images
- **16GB**: Default, suitable for most work
- **32GB**: Large catalogues, multiple images
- **64GB**: Very large survey datasets

!!! tip "Memory Planning"
    Large tables and multi-image layouts benefit from 32GB+ RAM. Start with 8GB and scale up if needed.

#### CPU Cores

Most Firefly work is I/O bound rather than CPU intensive:

- **2 cores**: Default, sufficient for most visualisation tasks
- **4 cores**: Large table operations, complex filtering

![Choose Firefly Cores](images/firefly/5_choose_firefly_cores.png)

### Step 4: Launch Session

1. **Click** "Launch" button
2. **Wait** for container initialization (~30-60 seconds)
3. **Session appears** on your portal dashboard
4. **Click** the session icon to access Firefly

![Launch Firefly](images/firefly/6_launch_firefly.png)

## 🔥 Using Firefly

### Interface Overview

Firefly's interface consists of several main areas:

```mermaid
graph TD
    Interface[Firefly Interface]
    Interface --> Upload["📁 File Upload Area"]
    Interface --> Images["🖼️ Image Display"]
    Interface --> Tables["📊 Table Viewer"]
    Interface --> Tools["🔧 Analysis Tools"]
    
    Upload --> Local[Local Files]
    Upload --> URLs[Remote URLs]
    Upload --> VOSpace[VOSpace Files]
    
    Images --> Display[Image Canvas]
    Images --> Controls[Display Controls]
    Images --> Overlays[Catalog Overlays]
    
    Tables --> Browse[Data Browser]
    Tables --> Filter[Filtering Tools]
    Tables --> Plot[Plotting Tools]
```

### Loading Data

#### Upload Local Files

**FITS Images:**

```text
1. Click "Images" tab
2. Select "Upload" 
3. Choose FITS file from your computer
4. Image loads automatically with WCS if available
```

**Catalogue Tables:**

```text
1. Click "Tables" tab
2. Select "Upload"
3. Choose CSV, FITS table, or VOTable
4. Table opens in browser interface
```

#### Access CANFAR Storage

**From `arc` Projects:**

```bash
# Files in your project directory are accessible via:
# /arc/projects/[projectname]/data/image.fits
# /arc/projects/[projectname]/data/image_sources.csv
```

**From VOSpace:**

```text
1. In Firefly, use "File" → "Open"
2. Navigate to VOSpace URLs
3. Access: vos://cadc.nrc.ca~vault/[projectname]/
```

#### Remote Data Access

**Survey Archives:**

```text
# Example URLs for Firefly
https://archive.stsci.edu/hlsp/data.fits
https://irsa.ipac.caltech.edu/data/WISE/cutouts/
```

### Image Analysis

#### Basic Image Display

```text
1. Load FITS image
2. Adjust stretch (log, linear, sqrt)
3. Set scale limits (min/max values)
4. Choose colour table (heat, cool, rainbow)
```

#### Multi-band RGB

```text
1. Load three images (e.g., g, r, i bands)
2. Select "RGB" mode
3. Assign each image to R, G, or B channel
4. Adjust relative scaling
```

#### Coordinate Systems

```text
# Firefly supports standard coordinate systems:
- Equatorial (RA/Dec) - J2000, B1950
- Galactic coordinates
- Ecliptic coordinates  
- Pixel coordinates
```

### Catalog Analysis

#### Table Operations

**Basic Navigation:**

```text
- Sort columns by clicking headers
- Filter rows using search box
- Select multiple rows with Ctrl+click
- Pan/zoom table with mouse wheel
```

**Advanced Filtering:**

```javascript
// Example filters (use in filter box):
magnitude < 20.5                    // Bright sources
colour_g_r > 0.5 && colour_g_r < 1.5  // Colour selection
distance < 100                      // Distance constraint
```

#### Plotting Tools

**Column Plots:**

```text
1. Select table columns for X and Y axes
2. Choose plot type (scatter, histogram, line)
3. Apply colour coding by third column
4. Add error bars if available
```

**Image-Catalogue Overlay:**

```text
1. Load image and catalogue table
2. Match coordinate columns (RA, Dec)
3. Select overlay symbol (circle, cross, diamond)
4. Adjust symbol size and colour
5. Sources appear overlaid on image
```

### Advanced Features

#### Cutout Services

Extract subimages from large surveys:

```text
# Using Firefly's cutout interface
1. Right-click on image location
2. Select "Create Cutout"
3. Specify size (arcmin)
4. Choose format (FITS, JPEG, PNG)
5. Download or save to VOSpace
```

#### Multi-wavelength Analysis

```text
1. Load images in different bands
2. Use "Blink" mode to compare
3. Create RGB composite
4. Overlay catalogue with colour-magnitude selection
5. Identify sources across wavelengths
```

#### Data Export

**Save Results:**

```text
- Modified tables → CSV, FITS, VOTable formats
- Image displays → PNG, PDF for publications  
- Analysis plots → Vector formats for papers
- Session state → Save/restore workspace
```

## 🛠️ Common Workflows

### Survey Photometry

```text
1. Load survey image (HSC, LSST, etc.)
2. Upload photometric catalogue
3. Overlay sources on image
4. Filter by magnitude and colour
5. Create colour-magnitude diagram
6. Export selected sources
```

### Multi-object Analysis

```text
1. Load target list (CSV with coordinates)
2. Create cutouts around each target
3. Measure properties in each cutout
4. Compile results in table
5. Plot trends and correlations
6. Save analysis products
```

### Time Series Visualisation

```text
1. Load time-series table (time, magnitude, error)
2. Create light curve plot
3. Apply period folding if needed
4. Identify outliers and trends
5. Export cleaned data
```

## 🔧 Integration with CANFAR

### Storage Access

**ARC Projects:**

```bash
# Your project data appears in Firefly file browser
/arc/projects/[projectname]/
├── images/           # FITS images
├── catalogue/        # Source tables  
├── results/          # Analysis products
└── plots/            # Exported figures
```

**VOSpace Integration:**

```bash
# Access archived data
vos://cadc.nrc.ca~vault/[projectname]/
├── published_data/   # Public datasets
├── working_data/     # Analysis in progress
└── final_products/   # Paper-ready results
```

### Collaborative Features

**Session Sharing:**

```text
1. Copy Firefly session URL
2. Share with team members (same CANFAR group)
3. Collaborate on analysis in real-time
4. Each user sees same data and visualisations
```

**Data Sharing:**

```text
1. Save analysis results to shared project space
2. Export publication-quality figures
3. Share VOSpace links for external collaborators
4. Version control important datasets
```

## 📊 Performance Tips

### Large Dataset Handling

**Memory Management:**

```text
- Load subsets of large catalogues first
- Use server-side filtering when possible
- Close unused tables and images
- Monitor memory usage in browser
```

**Network Optimisation:**

```text
- Use compressed file formats (gzip FITS)
- Access local files when possible (/arc/projects)
- Cache frequently used data locally
- Use cutout services for large images
```

### Visualisation Performance

**Image Display:**

```text
- Use appropriate image size for screen resolution
- Apply reasonable stretch limits
- Close unused image panels
- Use PNG format for screenshots
```

**Table Operations:**

```text
- Filter large tables before plotting
- Use sampling for very large datasets
- Index frequently used columns
- Batch operations when possible
```

## 🆘 Troubleshooting

### Common Issues

**Firefly Won't Load:**

```text
- Check browser compatibility (Chrome, Firefox recommended)
- Clear browser cache and cookies
- Disable browser extensions that might interfere
- Try incognito/private browsing mode
```

**Images Not Displaying:**

```text
- Verify FITS file format and WCS headers
- Check file permissions and accessibility
- Try loading smaller test image first
- Ensure sufficient memory allocation
```

**Tables Not Loading:**

```text
- Verify file format (CSV, FITS table, VOTable)
- Check column headers and data types
- Ensure proper delimiter in CSV files
- Try loading subset of data first
```

**Performance Issues:**

```text
- Reduce number of overlay sources
- Close unused browser tabs
- Increase session memory allocation
- Use more efficient file formats
```

## 🔗 External Resources


### Documentation

- **[Firefly User Guide](https://firefly-help.ipac.caltech.edu/)** - Comprehensive documentation
- **[LSST Science Pipelines](https://pipelines.lsst.io/)** - Integration with LSST tools
- **[IRSA Tutorials](https://irsa.ipac.caltech.edu/docs/tutorials/)** - Survey data tutorials


### Data Archives

- **[LSST Data Portal](https://data.lsst.cloud/)** - LSST survey data
- **[HSC Archive](https://hsc.mtk.nao.ac.jp/)** - Hyper Suprime-Cam data
- **[IRSA](https://irsa.ipac.caltech.edu/)** - Infrared survey data

## 🔗 What's Next?

Firefly works great with other CANFAR tools:

- **[Table Analysis →](../storage/vospace-api.md)** - Advanced catalog management
- **[Desktop Sessions →](launch-desktop.md)** - Use Firefly with other GUI tools
- **[Batch Processing →](../../batch-jobs.md)** - Automate large survey analysis
- **[Container Guide →](../../containers.md)** - Customize Firefly environment

---

!!! tip "Firefly Best Practices"
    1. **Start with small datasets** to learn the interface before tackling large surveys
    2. **Use appropriate memory** - large catalogues need more RAM than single images  
    3. **Save your work frequently** - export important results to `/arc/projects/`
    4. **Collaborate effectively** - share session URLs for real-time teamwork
    5. **Optimize performance** - close unused data and use efficient file formats
